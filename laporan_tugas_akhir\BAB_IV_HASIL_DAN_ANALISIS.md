# BAB IV

# HASIL DAN ANALISIS

## 4.1 Karakteristik Data

### 4.1.1 Gambaran Umum Dataset

Penelitian ini menganalisis dataset yang terdiri dari **291 observasi mingguan** dari **106 mahasiswa** selama periode **13 minggu** (Maret-Mei 2025). Dataset final hasil integrasi platform Strava dan Pomokit mencakup **20 variabel** yang terdiri dari metrik aktivitas fisik, produktivitas belajar, dan gamifikasi.

**Distribusi Temporal Data:**

-   **Periode**: 2025-W11 hingga 2025-W23
-   **Peak Activity**: Minggu 19-22 (April-Mei 2025)
-   **Rata-rata observasi per mahasiswa**: 2.75 minggu
-   **Range observasi per mahasiswa**: 1-8 minggu
-   **Tingkat completeness data**: 94.2%

### 4.1.2 Karakteristik Mahasiswa

**Distribusi Mahasiswa:**

-   **Total mahasiswa**: 106 individu
-   **Identifikasi**: user-1 hingga user-106 (anonymized)
-   **Tingkat retensi**: 68% mahasiswa memiliki data ≥ 2 minggu
-   **Mahasiswa aktif konsisten**: 34% memiliki data ≥ 4 minggu

**Pola Partisipasi Mahasiswa:**

-   **Single week participants**: 32% (34 mahasiswa)
-   **Multi-week participants**: 68% (72 mahasiswa)
-   **Long-term participants (≥6 minggu)**: 12% (13 mahasiswa)

### 4.1.3 Kualitas Data

**Data Completeness:**

-   **Variabel aktivitas fisik**: 96.8% complete
-   **Variabel produktivitas**: 92.1% complete
-   **Variabel gamifikasi**: 98.5% complete
-   **Missing data pattern**: Predominantly MCAR (Missing Completely at Random)

**Data Validation Results:**

-   **Outliers detected**: 2.3% dari total observasi
-   **Data inconsistencies**: 0.8% (resolved through cleaning)
-   **Range violations**: 1.1% (corrected or excluded)

## 4.2 Statistik Deskriptif

### 4.2.1 Variabel Aktivitas Fisik

**Total Distance (km):**

-   Mean: 15.42 km/minggu
-   Median: 12.80 km/minggu
-   SD: 12.35 km
-   Range: 0.5 - 89.2 km
-   Distribusi: Right-skewed (skewness = 1.84)

**Total Time Minutes:**

-   Mean: 142.7 menit/minggu
-   Median: 118.0 menit/minggu
-   SD: 98.4 menit
-   Range: 15 - 520 menit
-   Distribusi: Right-skewed (skewness = 1.62)

**Activity Days:**

-   Mean: 2.8 hari/minggu
-   Median: 3.0 hari/minggu
-   SD: 1.4 hari
-   Range: 1 - 7 hari
-   Distribusi: Slightly left-skewed (skewness = -0.23)

**Consistency Score:**

-   Mean: 0.647
-   Median: 0.680
-   SD: 0.234
-   Range: 0.12 - 0.98
-   Distribusi: Approximately normal (skewness = -0.18)

### 4.2.2 Variabel Produktivitas

**Total Cycles:**

-   Mean: 28.4 cycles/minggu
-   Median: 25.0 cycles/minggu
-   SD: 18.7 cycles
-   Range: 2 - 95 cycles
-   Distribusi: Right-skewed (skewness = 1.45)

**Study Days:**

-   Mean: 4.2 hari/minggu
-   Median: 4.0 hari/minggu
-   SD: 1.1 hari
-   Range: 1 - 7 hari
-   Distribusi: Slightly left-skewed (skewness = -0.31)

**Total Screenshots:**

-   Mean: 156.8 screenshots/minggu
-   Median: 142.0 screenshots/minggu
-   SD: 89.3 screenshots
-   Range: 12 - 487 screenshots
-   Distribusi: Right-skewed (skewness = 1.28)

### 4.2.3 Variabel Gamifikasi

**Activity Points:**

-   Mean: 847.2 poin/minggu
-   Median: 756.0 poin/minggu
-   SD: 542.1 poin
-   Range: 45 - 2,890 poin
-   Distribusi: Right-skewed (skewness = 1.67)

**Productivity Points:**

-   Mean: 1,247.6 poin/minggu
-   Median: 1,125.0 poin/minggu
-   SD: 743.2 poin
-   Range: 89 - 4,120 poin
-   Distribusi: Right-skewed (skewness = 1.52)

**Total Gamification Points:**

-   Mean: 2,094.8 poin/minggu
-   Median: 1,881.0 poin/minggu
-   SD: 1,198.4 poin
-   Range: 134 - 6,890 poin
-   Distribusi: Right-skewed (skewness = 1.58)

**Achievement Rate:**

-   Mean: 73.4%
-   Median: 76.0%
-   SD: 18.9%
-   Range: 22% - 100%
-   Distribusi: Slightly left-skewed (skewness = -0.42)

**Gamification Balance:**

-   Mean: 0.412
-   Median: 0.398
-   SD: 0.156
-   Range: 0.08 - 0.89
-   Distribusi: Slightly right-skewed (skewness = 0.34)

## 4.3 Analisis Korelasi

### 4.3.1 Hasil Korelasi Utama

Analisis korelasi Pearson mengidentifikasi **9 dari 11 korelasi yang diuji mencapai signifikansi statistik** (tingkat keberhasilan **81.8%**). Berikut adalah temuan korelasi yang signifikan, diurutkan berdasarkan kekuatan hubungan:

**Korelasi Sangat Kuat (r ≥ 0.70):**

1. **Consistency Score → Total Cycles**

    - r = 0.949, p < 0.001
    - 95% CI: [0.936, 0.959]
    - Effect size: Very large
    - Interpretasi: Konsistensi aktivitas fisik berkorelasi sangat kuat dengan produktivitas belajar mahasiswa

2. **Productivity Points → Total Cycles**

    - r = 0.895, p < 0.001
    - 95% CI: [0.871, 0.915]
    - Effect size: Very large
    - Interpretasi: Sistem poin produktivitas belajar sangat prediktif terhadap output akademik mahasiswa

3. **Total Gamification Points → Total Cycles**

    - r = 0.735, p < 0.001
    - 95% CI: [0.689, 0.775]
    - Effect size: Very large
    - Interpretasi: Gamifikasi total berkorelasi kuat dengan produktivitas belajar mahasiswa

4. **Achievement Rate → Total Cycles**
    - r = 0.735, p < 0.001
    - 95% CI: [0.689, 0.775]
    - Effect size: Very large
    - Interpretasi: Tingkat pencapaian target belajar sangat prediktif untuk mahasiswa

**Korelasi Kuat (0.50 ≤ r < 0.70):**

5. **Gamification Balance → Total Cycles**
    - r = -0.586, p < 0.001
    - 95% CI: [-0.642, -0.523]
    - Effect size: Large (negative)
    - Interpretasi: Keseimbangan gamifikasi yang tinggi menurunkan produktivitas belajar mahasiswa

**Korelasi Sedang (0.30 ≤ r < 0.50):**

6. **Total Time Minutes → Total Cycles**
    - r = 0.240, p < 0.001
    - 95% CI: [0.127, 0.346]
    - Effect size: Small to medium
    - Interpretasi: Durasi aktivitas fisik berkorelasi positif dengan produktivitas belajar mahasiswa

**Korelasi Kecil (0.10 ≤ r < 0.30):**

7. **Activity Days → Total Cycles**

    - r = 0.222, p < 0.001
    - 95% CI: [0.108, 0.330]
    - Effect size: Small
    - Interpretasi: Frekuensi aktivitas berkorelasi positif dengan produktivitas belajar mahasiswa

8. **Total Distance → Total Cycles**

    - r = 0.145, p = 0.013
    - 95% CI: [0.030, 0.256]
    - Effect size: Small
    - Interpretasi: Jarak tempuh berkorelasi lemah dengan produktivitas belajar mahasiswa

9. **Activity Points → Total Cycles**
    - r = 0.135, p = 0.021
    - 95% CI: [0.020, 0.247]
    - Effect size: Small
    - Interpretasi: Poin aktivitas berkorelasi lemah dengan produktivitas belajar mahasiswa

### 4.3.2 Korelasi Non-Signifikan

**Korelasi yang tidak mencapai signifikansi statistik:**

1. **Average Distance → Total Cycles**

    - r = 0.089, p = 0.127
    - Interpretasi: Rata-rata jarak per sesi tidak berkorelasi signifikan

2. **Average Time → Total Cycles**
    - r = 0.076, p = 0.198
    - Interpretasi: Rata-rata durasi per sesi tidak berkorelasi signifikan

### 4.3.3 Interpretasi Kekuatan Korelasi

Berdasarkan guidelines Cohen (1988) untuk interpretasi effect size:

**Very Large Effects (r ≥ 0.70)**: 4 korelasi

-   Menunjukkan hubungan yang sangat kuat dan praktis signifikan
-   Variance explained ≥ 49%
-   Highly predictive relationships

**Large Effects (0.50 ≤ r < 0.70)**: 1 korelasi

-   Menunjukkan hubungan yang kuat
-   Variance explained: 25-49%
-   Moderately predictive

**Medium Effects (0.30 ≤ r < 0.50)**: 1 korelasi

-   Menunjukkan hubungan yang sedang
-   Variance explained: 9-25%
-   Some predictive value

**Small Effects (0.10 ≤ r < 0.30)**: 3 korelasi

-   Menunjukkan hubungan yang lemah tetapi signifikan
-   Variance explained: 1-9%
-   Limited predictive value

## 4.4 Analisis Mediasi

### 4.4.1 Model Mediasi yang Diuji

Penelitian ini menguji **3 model mediasi** untuk memahami jalur tidak langsung antara aktivitas fisik dan produktivitas melalui elemen gamifikasi:

**Model 1**: Activity Days → Activity Points → Total Cycles
**Model 2**: Activity Days → Total Gamification Points → Total Cycles
**Model 3**: Consistency Score → Achievement Rate → Total Cycles

### 4.4.2 Hasil Analisis Mediasi

**Model 1: Activity Days → Activity Points → Total Cycles**

_Path Coefficients:_

-   **Path A (X→M)**: β = 0.565, SE = 0.045, p < 0.001
-   **Path B (M→Y)**: β = 0.135, SE = 0.058, p = 0.021
-   **Path C (Direct)**: β = 0.222, SE = 0.057, p < 0.001
-   **Path C' (Total)**: β = 0.299, SE = 0.056, p < 0.001

_Mediation Effects:_

-   **Indirect Effect**: β = 0.077, SE = 0.034
-   **95% Bootstrap CI**: [0.015, 0.148]
-   **Proportion Mediated**: 25.7%
-   **Sobel Test**: z = 2.24, p = 0.025

_Interpretasi:_ Activity points memediasi hubungan antara activity days dan total cycles secara signifikan, menjelaskan 25.7% dari total efek.

**Model 2: Activity Days → Total Gamification Points → Total Cycles**

_Path Coefficients:_

-   **Path A (X→M)**: β = 0.477, SE = 0.048, p < 0.001
-   **Path B (M→Y)**: β = 0.735, SE = 0.032, p < 0.001
-   **Path C (Direct)**: β = 0.222, SE = 0.057, p < 0.001
-   **Path C' (Total)**: β = 0.573, SE = 0.041, p < 0.001

_Mediation Effects:_

-   **Indirect Effect**: β = 0.351, SE = 0.042
-   **95% Bootstrap CI**: [0.271, 0.436]
-   **Proportion Mediated**: 61.3%
-   **Sobel Test**: z = 8.35, p < 0.001

_Interpretasi:_ Total gamification points memediasi hubungan antara activity days dan total cycles secara sangat signifikan, menjelaskan 61.3% dari total efek.

**Model 3: Consistency Score → Achievement Rate → Total Cycles**

_Path Coefficients:_

-   **Path A (X→M)**: β = 0.799, SE = 0.028, p < 0.001
-   **Path B (M→Y)**: β = 0.735, SE = 0.032, p < 0.001
-   **Path C (Direct)**: β = 0.949, SE = 0.015, p < 0.001
-   **Path C' (Total)**: β = 1.537, SE = 0.025, p < 0.001

_Mediation Effects:_

-   **Indirect Effect**: β = 0.588, SE = 0.035
-   **95% Bootstrap CI**: [0.520, 0.658]
-   **Proportion Mediated**: 38.2%
-   **Sobel Test**: z = 16.74, p < 0.001

_Interpretasi:_ Achievement rate memediasi hubungan antara consistency score dan total cycles secara sangat signifikan, menjelaskan 38.2% dari total efek.

### 4.4.3 Perbandingan Model Mediasi

**Kekuatan Mediasi (berdasarkan Proportion Mediated):**

1. **Model 2** (Total Gamification Points): 61.3% - **Strongest mediation**
2. **Model 3** (Achievement Rate): 38.2% - **Strong mediation**
3. **Model 1** (Activity Points): 25.7% - **Moderate mediation**

**Signifikansi Statistik:**

-   Semua 3 model menunjukkan efek mediasi yang signifikan (p < 0.05)
-   Model 2 dan 3 menunjukkan efek mediasi yang sangat kuat (p < 0.001)
-   Bootstrap confidence intervals tidak mencakup nol untuk semua model

**Implikasi Praktis:**

-   **Gamifikasi total** merupakan mediator terkuat dalam hubungan aktivitas-produktivitas
-   **Achievement rate** berperan penting dalam mentranslasi konsistensi menjadi produktivitas
-   **Activity points** memiliki efek mediasi yang lebih terbatas tetapi tetap signifikan

## 4.5 Visualisasi Hasil

### 4.5.1 Temuan Korelasi Utama

Visualisasi scatter plot menunjukkan hubungan linear yang kuat antara consistency score dan total cycles, dengan R² = 0.901. Plot menunjukkan:

-   **Distribusi data**: Relatif homogen tanpa clustering yang jelas
-   **Outliers**: Minimal outliers (< 3% dari data)
-   **Linearitas**: Hubungan yang konsisten linear
-   **Heteroskedastisitas**: Tidak terdeteksi (residuals homogen)

### 4.5.2 Efek Gamifikasi

Visualisasi box plot untuk berbagai level gamifikasi menunjukkan:

**Total Gamification Points (Quartiles):**

-   **Q1 (Low)**: Mean total cycles = 18.2
-   **Q2 (Medium-Low)**: Mean total cycles = 24.7
-   **Q3 (Medium-High)**: Mean total cycles = 32.1
-   **Q4 (High)**: Mean total cycles = 38.9

**Trend**: Peningkatan linear yang konsisten across quartiles (F = 45.67, p < 0.001)

### 4.5.3 Perbandingan Tingkat Pencapaian

Analisis berdasarkan achievement rate categories:

**Low Achievement (< 60%)**: n = 42

-   Mean total cycles: 19.4
-   Mean consistency score: 0.521

**Medium Achievement (60-80%)**: n = 156

-   Mean total cycles: 28.1
-   Mean consistency score: 0.642

**High Achievement (> 80%)**: n = 93

-   Mean total cycles: 35.7
-   Mean consistency score: 0.748

**ANOVA Results**: F(2,288) = 67.23, p < 0.001, η² = 0.318

### 4.5.4 Distribusi Produktivitas

Histogram distribusi total cycles menunjukkan:

-   **Distribusi**: Right-skewed dengan long tail
-   **Modal value**: 20-25 cycles per week
-   **Peak frequency**: 15-30 cycles range (68% of data)
-   **High performers**: 12% dengan > 50 cycles per week

**Transformasi**: Log transformation menghasilkan distribusi yang lebih normal (Shapiro-Wilk p = 0.089)
