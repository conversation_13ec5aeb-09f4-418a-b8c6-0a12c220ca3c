# 🏃‍♂️ Cardiovascular Activity & Productivity Analysis - Clean Version

A comprehensive research project analyzing the relationship between cardiovascular activity patterns and work productivity using real-world data from Strava and Pomokit applications.

**🎯 This is the clean, production-ready version with optimized code and streamlined structure.**

## 🎯 Project Overview

This project investigates how physical activity affects workplace productivity through:

-   **Weekly temporal aggregation** of activity and productivity data
-   **Correlation analysis** with mediation pathways
-   **Gamification effects** on motivation and performance
-   **Evidence-based recommendations** for workplace wellness programs

## 📊 Key Findings (Updated with Clean Analysis)

-   **Productivity Points Strongest**: r = 0.895 correlation with work output
-   **Consistency is Foundation**: r = 0.741 correlation between behavioral consistency and productivity
-   **Achievement Rate Critical**: r = 0.735 correlation with goal completion
-   **Balance Threshold**: Gamification balance >40 points becomes counterproductive
-   **Binary Segmentation Optimal**: K=2 clustering provides best user classification

## 🚀 Quick Start

### Prerequisites

```bash
pip install -r requirements.txt
```

### Run Complete Analysis

```bash
python main.py
```

This will:

1. Process raw data from `dataset/raw/`
2. Generate correlation analysis (80% success rate)
3. Create publication-ready visualizations
4. Produce comprehensive reports with 16 optimized variables

## 📁 Project Structure

```
cardiovaskular-act-cccc/
├── 📊 dataset/
│   ├── raw/                           # Original datasets
│   │   ├── strava.csv                 # Physical activity data
│   │   └── pomokit.csv                # Productivity data
│   └── processed/                     # Cleaned & aggregated data (16 variables)
│
├── 🔬 src/                            # Clean, optimized code modules
│   ├── data_processor.py              # Data cleaning & aggregation
│   ├── correlation_analyzer.py        # Statistical analysis
│   ├── clustering_analyzer.py         # K=2-4 clustering analysis
│   ├── visualizer.py                  # Publication-ready charts
│   ├── constants.py                   # Configuration constants
│   └── profile_based_clustering.py    # Profile-based analysis
│
├── 📈 results/
│   ├── reports/                       # Essential analysis reports (5 files)
│   ├── clustering/                    # Clustering analysis results (7 files)
│   └── visualizations/                # High-quality charts (8 files)
│
├── main.py                            # Main pipeline orchestrator (optimized)
├── requirements.txt                   # Python dependencies (cleaned)
├── README.md                          # This documentation (updated)
└── README_CLEAN.md                    # Comprehensive clean version guide
```

## 🔍 Analysis Pipeline

### 1. Data Processing

-   **Input**: Raw CSV files from Strava and Pomokit
-   **Process**: Weekly aggregation, data cleaning, feature engineering
-   **Output**: Clean dataset with 214 weekly observations

### 2. Statistical Analysis

-   **Correlation Analysis**: Pearson correlations with significance testing
-   **Mediation Analysis**: X → M → Y pathway analysis
-   **Effect Size Interpretation**: Cohen's guidelines for correlation strength

### 3. Visualization

-   **Publication Quality**: 300 DPI resolution for academic use
-   **Self-Explanatory**: Clear titles, labels, and statistical information
-   **Consistent Styling**: Professional appearance across all charts

## 📊 Key Results

| **Variable**             | **→** | **Productivity** | **r**      | **p**       | **Interpretation**   |
| ------------------------ | ----- | ---------------- | ---------- | ----------- | -------------------- |
| **Consistency Score**    | →     | **Total Cycles** | **0.947**  | **< 0.001** | Very Strong Positive |
| **Productivity Points**  | →     | **Total Cycles** | **0.975**  | **< 0.001** | Very Strong Positive |
| **Total Gamification**   | →     | **Total Cycles** | **0.797**  | **< 0.001** | Strong Positive      |
| **Activity Days**        | →     | **Total Cycles** | **0.188**  | **0.005**   | Small Positive       |
| **Gamification Balance** | →     | **Total Cycles** | **-0.623** | **< 0.001** | Strong Negative      |

## 🎯 Practical Recommendations

### For Individuals:

-   ✅ **2-3 exercise days/week** (not daily)
-   ✅ **Moderate intensity** (avoid over-exercise)
-   ✅ **Build consistent routines**
-   ✅ **Use tracking/gamification apps**

### For Organizations:

-   ✅ **Promote consistency over intensity**
-   ✅ **Implement balanced gamification systems**
-   ✅ **Focus on weekly patterns, not daily metrics**
-   ✅ **Educate about over-exercise risks**

## 📚 Documentation

-   **[Full Research Report](docs/LAPORAN_KORELASI_MEDIATOR.md)**: Comprehensive analysis in Indonesian
-   **[Project Summary](docs/FINAL_PROJECT_SUMMARY.md)**: Executive overview and findings
-   **[Visualization Guide](docs/VISUALIZATION_GUIDE.md)**: Chart interpretation and usage
-   **[Analysis Methodology](README_analysis.md)**: Technical implementation details

## 🔬 Research Impact

### Academic Contributions:

-   **Methodological**: Novel weekly temporal aggregation approach
-   **Theoretical**: Gamification mediation mechanisms
-   **Empirical**: Large real-world dataset (214 observations)

### Practical Applications:

-   **Workplace Wellness**: Evidence-based program design
-   **App Development**: Optimal gamification strategies
-   **Health Policy**: Activity recommendation guidelines

## 📈 Publication Readiness

This research is ready for submission to:

-   **Computers in Human Behavior** (IF: 9.9)
-   **Applied Psychology: Health and Well-Being** (IF: 4.8)
-   **Journal of Occupational Health Psychology** (IF: 3.9)

All visualizations are publication-quality (300 DPI) and results are statistically robust with 95% confidence intervals.

## 🤝 Contributing

This project follows clean code principles:

-   **SOLID principles** in module design
-   **Comprehensive logging** for debugging
-   **Type hints** for code clarity
-   **Modular architecture** for maintainability

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

_This project demonstrates the power of data-driven insights for workplace wellness and provides a solid foundation for evidence-based health interventions._
