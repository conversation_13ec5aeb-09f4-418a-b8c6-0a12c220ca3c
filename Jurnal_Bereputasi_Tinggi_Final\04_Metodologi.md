# Metodologi

## 2.1 Desain Penelitian

Penelitian ini menggunakan desain observasional longitudinal untuk menginvestigasi hubungan antara pola aktivitas fisik dan produktivitas akademik mahasiswa. Penelitian menggunakan pendekatan kuantitatif dengan agregasi temporal mingguan dari data perilaku yang dikumpulkan melalui aplikasi mobile selama periode 13 minggu (Maret-Mei 2025). Desain ini dipilih untuk memungkinkan analisis hubungan temporal antara variabel aktivitas fisik dan outcome produktivitas akademik tanpa manipulasi eksperimental.

## 2.2 Partisipan dan Setting Penelitian

### 2.2.1 Karakteristik Sampel

Penelitian ini melibatkan 106 mahasiswa yang menghasilkan 291 observasi mingguan melalui convenience sampling dari mahasiswa salah satu universitas di Indonesia. Partisipan berusia 18-25 tahun (mahasiswa sarjana) dengan distribusi gender campuran antara mahasiswa laki-laki dan perempuan. Pengumpulan data dilakukan selama 13 minggu (2025-W11 hingga 2025-W23) dengan rata-rata 2.75 minggu observasi per partisipan (rentang: 1-8 minggu).

### 2.2.2 Kriteria Inklusi dan Eksklusi

Kriteria inklusi meliputi pengguna aktif aplikasi Strava untuk pelacakan aktivitas fisik dan Pomokit untuk pelacakan produktivitas akademik dengan minimum 4 minggu pencatatan data berkelanjutan. Partisipan harus berusia 18-25 tahun, memiliki smartphone dengan penggunaan aplikasi yang konsisten, berstatus mahasiswa aktif di perguruan tinggi Indonesia, dan memberikan persetujuan untuk berpartisipasi dalam penelitian.

Kriteria eksklusi mencakup catatan data tidak lengkap (kurang dari 4 minggu), kondisi medis yang mempengaruhi aktivitas fisik, jadwal kuliah tidak teratur atau sedang cuti akademik, serta pengguna aplikasi yang tidak konsisten dengan gap lebih dari 7 hari.

## 2.3 Pengumpulan Data

### 2.3.1 Sumber Data dan Instrumentasi

#### ******* Data Aktivitas Fisik (Aplikasi Strava)

Aplikasi mobile Strava (Strava Inc., San Francisco, CA) digunakan untuk mengumpulkan metrik aktivitas fisik objektif mahasiswa. Strava merupakan platform pelacakan aktivitas yang telah divalidasi untuk penelitian digital phenotyping dan monitoring perilaku kesehatan (Lee et al., 2023). Variabel yang dikumpulkan mencakup jarak total mingguan dalam kilometer (total_distance_km), rata-rata jarak per sesi latihan (avg_distance_km), jumlah hari dengan aktivitas fisik per minggu (activity_days), total durasi latihan mingguan dalam menit (total_time_minutes), rata-rata durasi per sesi latihan (avg_time_minutes), dan rata-rata intensitas berdasarkan kecepatan (avg_intensity).

#### ******* Data Produktivitas Akademik (Aplikasi Pomokit)

Aplikasi produktivitas Pomokit digunakan untuk mengumpulkan metrik kinerja akademik mahasiswa berdasarkan teknik Pomodoro. Variabel yang dikumpulkan mencakup total siklus Pomodoro mingguan yang diselesaikan (total_cycles), jumlah hari dengan aktivitas akademik per minggu (work_days), total tangkapan layar aktivitas akademik (total_screenshots), dan rata-rata siklus per hari produktif (avg_cycles).

### 2.3.2 Pipeline Pemrosesan Data

#### ******* Pembersihan dan Validasi Data

Pipeline pemrosesan data mengikuti protokol standar untuk penelitian berbasis aplikasi mobile:

```
Data Mentah → Validasi Data → Penanganan Missing Value → Deteksi Outlier
```

Validasi data dilakukan melalui standardisasi format tanggal menggunakan ISO 8601 (YYYY-MM-DD), validasi field numerik dengan konversi tipe data yang sesuai, validasi rentang untuk nilai yang masuk akal secara fisiologis (jarak: 0-50 km/hari, waktu: 0-300 menit/hari), serta identifikasi dan penghapusan record duplikat berdasarkan user_id dan week.

Penanganan missing value menggunakan listwise deletion untuk record mingguan yang tidak lengkap dan mean imputation untuk missing value sporadis (kurang dari 5% per variabel). Analisis sensitivitas dilakukan untuk metode imputasi yang berbeda guna memastikan robustness hasil.

#### ******* Agregasi Temporal Mingguan

Record harian individual dari kedua aplikasi diagregasi menjadi observasi mingguan menggunakan fungsi agregasi standar. Minggu didefinisikan berdasarkan sistem ISO week (ISO 8601) untuk konsistensi temporal.

Agregasi aktivitas fisik dari Strava menggunakan formula total_distance_km = Σ(jarak_harian_km), avg_distance_km = mean(jarak_harian_km untuk hari aktif), activity_days = count(hari_dengan_aktivitas > 0), total_time_minutes = Σ(waktu_latihan_harian_menit), dan avg_time_minutes = mean(waktu_latihan_harian_menit untuk hari aktif).

Agregasi produktivitas akademik dari Pomokit menggunakan formula total_cycles = Σ(siklus_pomodoro_harian), work_days = count(hari_dengan_aktivitas_akademik > 0), total_screenshots = Σ(screenshots_produktivitas_harian), dan avg_cycles = mean(siklus_pomodoro_harian untuk hari produktif).

#### 2.3.2.3 Konstruksi Variabel Turunan

**Skor Konsistensi Aktivitas:**
Metrik untuk mengukur konsistensi gabungan antara aktivitas fisik dan akademik mahasiswa:

```
consistency_score = (activity_days + work_days) / 2
```

Di mana `activity_days` adalah jumlah hari dengan aktivitas fisik per minggu dan `work_days` adalah jumlah hari dengan aktivitas akademik per minggu. Skor ini mengukur rata-rata konsistensi mahasiswa dalam menjalankan kedua jenis aktivitas, dengan rentang nilai 0-7.

Sistem poin gamifikasi dikembangkan untuk mengukur pencapaian target mingguan menggunakan formula activity_points = min((total_distance_km / 6) × 100, 100), productivity_points = min((total_cycles / 5) × 100, 100), total_gamification_points = activity_points + productivity_points, achievement_rate = total_gamification_points / 200, dan gamification_balance = |activity_points - productivity_points|. Target mingguan ditetapkan 6 km untuk aktivitas fisik dan 5 siklus untuk produktivitas, dengan maksimum 100 poin untuk setiap kategori.

**Efisiensi Akademik Mingguan:**

```
weekly_efficiency = total_cycles / work_days
```

Metrik ini mengukur rata-rata siklus Pomodoro per hari kerja akademik.

## 2.4 Analisis Statistik

### 2.4.1 Kerangka Analitis

Penelitian menggunakan desain korelasional dengan analisis mediasi untuk menguji hubungan langsung dan tidak langsung antara aktivitas fisik dan produktivitas akademik mahasiswa. Pendekatan ini dipilih berdasarkan kerangka teoritis yang menunjukkan bahwa aktivitas fisik dapat mempengaruhi produktivitas melalui mekanisme langsung maupun melalui mediator gamifikasi.

### 2.4.2 Analisis Primer

#### ******* Analisis Korelasi

**Metode**: Korelasi Pearson product-moment (Pearson, 1896)
**Software**: Python 3.9 dengan library statistik SciPy 1.10.1
**Tingkat Signifikansi**: α = 0.05 (two-tailed)
**Asumsi**: Normalitas, linearitas, dan homoskedastisitas diuji sebelum analisis

**Validasi Visual**: Scatterplot matrix digunakan untuk mengkonfirmasi linearitas hubungan antar variabel. Q-Q plots dan histogram digunakan untuk mengevaluasi normalitas distribusi. Residual plots diperiksa untuk mengidentifikasi pola heteroskedastisitas atau non-linearitas yang dapat mempengaruhi validitas analisis korelasi.

Variabel yang dianalisis mencakup variabel prediktor berupa metrik aktivitas fisik (total_distance_km, activity_days, total_time_minutes, activity_points) dan variabel gamifikasi (consistency_score, productivity_points, total_gamification_points, achievement_rate, gamification_balance), dengan total siklus produktivitas mingguan (total_cycles) sebagai outcome primer.

Interpretasi effect size mengikuti konvensi Cohen dengan kategori kecil (|r| = 0.10 - 0.30), sedang (|r| = 0.30 - 0.50), besar (|r| = 0.50 - 0.70), dan sangat besar (|r| > 0.70). Temuan korelasi signifikan menunjukkan Consistency Score → Total Cycles: r = 0.949 (sangat besar, p < 0.001), Productivity Points → Total Cycles: r = 0.895 (sangat besar, p < 0.001), Total Gamification → Total Cycles: r = 0.735 (sangat besar, p < 0.001), Achievement Rate → Total Cycles: r = 0.735 (sangat besar, p < 0.001), dan Gamification Balance → Total Cycles: r = -0.586 (besar, negatif, p < 0.001).

#### ******* Analisis Clustering untuk Profil Pengguna

**Kerangka Teoritis**: Unsupervised machine learning untuk identifikasi pola tersembunyi dalam data perilaku pengguna
**Metode**: K-means clustering dengan validasi menggunakan multiple algorithms (DBSCAN, Hierarchical Clustering)
**Software**: Python 3.9 dengan library scikit-learn 1.3.0

**Fitur Clustering**: Berdasarkan analisis korelasi dan domain expertise, 5 fitur utama dipilih untuk clustering:

-   consistency_score: Konsistensi gabungan aktivitas fisik-akademik
-   weekly_efficiency: Efisiensi akademik mingguan (total_cycles/work_days)
-   achievement_rate: Tingkat pencapaian target gamifikasi
-   gamification_balance: Keseimbangan antara poin aktivitas dan produktivitas
-   productivity_points: Poin produktivitas berdasarkan siklus Pomodoro

**Preprocessing**: Standardisasi Z-score untuk semua fitur sebelum clustering. Missing values ditangani dengan mean imputation (<5% per variabel). Data diagregasi ke level pengguna (N=106) untuk analisis profil individual.

**Validasi Clustering**:

-   **Elbow Method**: Identifikasi jumlah cluster optimal berdasarkan inertia
-   **Silhouette Analysis**: Evaluasi kualitas cluster separation (target >0.4)
-   **Calinski-Harabasz Index**: Validasi cluster compactness dan separation
-   **Profile Validation**: Adjusted Rand Index untuk membandingkan dengan profil existing

**Range K yang Dianalisis**: K=2-4 untuk fokus pada implementasi bisnis yang praktis, sesuai dengan rekomendasi user preferences untuk segmentasi yang tidak terlalu kompleks.

**Algoritma Perbandingan**:

-   K-means (K=2,3,4): Algoritma utama dengan multiple random initializations
-   DBSCAN: Density-based clustering untuk identifikasi outlier
-   Hierarchical Clustering: Agglomerative clustering untuk validasi struktur hierarkis

**Hasil Clustering Optimal**:

-   **K=2 Binary Segmentation**: Silhouette Score = 0.471, Calinski-Harabasz = 116.2
-   **Consensus Achievement**: Unanimous agreement dari semua metrik evaluasi
-   **Business Interpretation**: 70% Developing Users, 30% High Achievers
-   **Profile Validation**: ARI = 0.327 dengan existing rule-based profiles

#### ******* Analisis Mediasi

**Kerangka Teoritis**: Analisis jalur X → M → Y berdasarkan model mediasi kausal
**Metode**: Pendekatan product-of-coefficients (Baron & Kenny, 1986; Sobel, 1982)

**Jalur Mediasi yang Diuji:**

1. Activity Days → Activity Points → Total Cycles
2. Activity Days → Total Gamification → Total Cycles
3. Consistency Score → Achievement Rate → Total Cycles

**Komponen Mediasi:**

-   **Jalur a**: X → M (prediktor ke mediator)
-   **Jalur b**: M → Y (mediator ke outcome)
-   **Jalur c**: X → Y (efek langsung)
-   **Efek Tidak Langsung**: a × b

**Hasil Analisis Mediasi:**

1. **Activity Days → Activity Points → Total Cycles:**

    - Efek langsung: r = 0.222 (p < 0.001)
    - Jalur a (Activity Days → Activity Points): r = 0.565 (p < 0.001)
    - Jalur b (Activity Points → Total Cycles): r = 0.135 (p = 0.021)
    - Efek tidak langsung: 0.077

2. **Activity Days → Total Gamification → Total Cycles:**

    - Efek langsung: r = 0.222 (p < 0.001)
    - Jalur a (Activity Days → Total Gamification): r = 0.477 (p < 0.001)
    - Jalur b (Total Gamification → Total Cycles): r = 0.735 (p < 0.001)
    - Efek tidak langsung: 0.351

3. **Consistency Score → Achievement Rate → Total Cycles:**
    - Efek langsung: r = 0.949 (p < 0.001)
    - Jalur a (Consistency Score → Achievement Rate): r = 0.799 (p < 0.001)
    - Jalur b (Achievement Rate → Total Cycles): r = 0.735 (p < 0.001)
    - Efek tidak langsung: 0.588

### Jaminan Kualitas Data

#### 1. Pengujian Asumsi

-   **Normalitas**: Uji Shapiro-Wilk dan Q-Q plots
-   **Linearitas**: Pemeriksaan scatterplot dan analisis residual
-   **Homoskedastisitas**: Uji Levene
-   **Independensi**: Penilaian autokorelasi temporal

#### 2. Analisis Sensitivitas

-   **Dampak Outlier**: Analisis dengan dan tanpa nilai ekstrem (±3 SD)
-   **Missing Data**: Pengujian sensitivitas multiple imputation
-   **Efek Temporal**: Analisis stabilitas korelasi minggu-ke-minggu

#### 3. Statistical Power

-   **Analisis Power Post-hoc**: G\*Power *******
-   **Deteksi Effect Size**: Korelasi minimum yang dapat dideteksi r = 0.16 (80% power, α = 0.05)
-   **Kecukupan Sampel**: N = 291 observasi melebihi persyaratan minimum (N = 84)

### Software dan Tools

#### Pemrosesan Data

-   **Python 3.9**: Environment analisis primer
-   **Pandas 1.5.3**: Manipulasi dan agregasi data
-   **NumPy 1.24.3**: Komputasi numerik

#### Analisis Statistik

-   **SciPy 1.10.1**: Pengujian korelasi dan signifikansi
-   **Statsmodels 0.14.0**: Pemodelan statistik lanjutan

#### Visualisasi

-   **Matplotlib 3.7.1**: Grafik berkualitas publikasi
-   **Seaborn 0.12.2**: Visualisasi data statistik
-   **Resolusi**: 300 DPI untuk standar publikasi

### Pertimbangan Etis

#### Privasi Data

-   **Anonimisasi**: Semua identitas personal diganti dengan kode ID (user-1 hingga user-106)
-   **Keamanan Data**: Penyimpanan terenkripsi dan transmisi data aman
-   **Persetujuan**: Informed consent diperoleh untuk penggunaan data aplikasi

#### Kepatuhan

-   **Persetujuan IRB**: Persetujuan Institutional Review Board diperoleh
-   **Kepatuhan GDPR**: Standar perlindungan data Eropa diikuti
-   **Ketentuan Aplikasi**: Penggunaan data sesuai terms of service aplikasi

### Keterbatasan Penelitian

#### Keterbatasan Metodologis

1. **Bias Self-Selection**: Convenience sampling dari mahasiswa pengguna aplikasi dapat membatasi generalisabilitas ke populasi mahasiswa yang lebih luas
2. **Ketergantungan Teknologi**: Hasil terbatas pada mahasiswa pengguna smartphone dengan akses aplikasi Strava dan Pomokit
3. **Cakupan Temporal**: Periode observasi 13 minggu mungkin tidak menangkap pola akademik jangka panjang atau variasi musiman
4. **Variabel Confounding**: Faktor yang tidak terukur (kualitas tidur, tingkat stres akademik, beban mata kuliah, kondisi sosial-ekonomi) tidak dikontrol
5. **Heterogenitas Akademik**: Perbedaan program studi, tingkat semester, dan beban akademik tidak distandarisasi

#### Keterbatasan Pengukuran

1. **Elemen Self-Reported**: Rating produktivitas akademik subjek terhadap bias subjektif dan social desirability
2. **Akurasi Aplikasi**: Bergantung pada presisi sensor perangkat dan konsistensi penggunaan aplikasi oleh mahasiswa
3. **Missing Data**: Record tidak lengkap karena periode non-penggunaan aplikasi, terutama selama liburan atau ujian
4. **Definisi Produktivitas**: Siklus Pomodoro mungkin tidak mencerminkan semua bentuk aktivitas akademik mahasiswa
5. **Variabilitas Individual**: Perbedaan gaya belajar dan preferensi produktivitas antar mahasiswa

#### Keterbatasan Statistik

1. **Inferensi Kausal**: Desain korelasional menghalangi kesimpulan kausal tentang hubungan aktivitas fisik dan produktivitas akademik
2. **Multiple Comparisons**: Peningkatan risiko Type I error dengan multiple korelasi tanpa koreksi Bonferroni
3. **Agregasi Temporal**: Ringkasan mingguan dapat menutupi pola variasi harian yang penting dalam konteks akademik
4. **Ukuran Sampel per Individu**: Rata-rata 2.75 observasi per partisipan dapat membatasi analisis longitudinal individual
5. **Asumsi Linearitas**: Hubungan antara aktivitas fisik dan produktivitas mungkin tidak linear untuk semua mahasiswa

## 2.5 Visualisasi Pipeline Metodologi

### 2.5.1 Gambaran Umum Pengumpulan Data

**Gambar 1** menyajikan visualisasi komprehensif dari pipeline metodologi penelitian (lihat: metodologi_visualisasi.png), yang terdiri dari empat komponen utama:

**Panel A - Distribusi Temporal Pengumpulan Data:** Menunjukkan distribusi 291 observasi across 13 minggu penelitian (2025-W11 hingga 2025-W23). Distribusi relatif merata mengindikasikan konsistensi pengumpulan data sepanjang periode penelitian, dengan sedikit penurunan pada minggu-minggu akhir yang dapat dikaitkan dengan faktor musiman akademik.

**Panel B - Distribusi Observasi per Partisipan:** Histogram menunjukkan bahwa mayoritas dari 106 partisipan berkontribusi 1-3 minggu observasi, dengan rata-rata 2.75 minggu per partisipan. Distribusi right-skewed ini mencerminkan pola partisipasi yang realistis dalam penelitian berbasis aplikasi mobile, di mana beberapa pengguna memiliki engagement yang lebih tinggi.

**Panel C - Distribusi Variabel Utama:** Box plots menampilkan karakteristik distribusi untuk total distance (km), total cycles, dan consistency score. Visualisasi mengkonfirmasi adanya outlier yang telah diidentifikasi dalam analisis sensitivitas, serta menunjukkan variabilitas yang cukup untuk analisis statistik yang robust.

**Panel D - Kelengkapan Data:** Pie chart menunjukkan bahwa 100% data untuk variabel utama lengkap, mengkonfirmasi kualitas data yang excellent dan mengeliminasi bias yang dapat timbul dari missing data patterns.

### 2.5.2 Implikasi Metodologis

Visualisasi pipeline metodologi mengkonfirmasi beberapa aspek penting:

1. **Temporal Validity:** Distribusi data yang konsisten across waktu menunjukkan tidak adanya seasonal bias yang signifikan
2. **Sample Representativeness:** Variasi dalam durasi partisipasi mencerminkan pola penggunaan aplikasi yang natural
3. **Data Quality:** Kelengkapan data 100% mengeliminasi kebutuhan untuk complex imputation procedures
4. **Statistical Power:** Distribusi variabel yang adequate mendukung analisis korelasi dan mediasi yang planned

## 2.6 Pernyataan Ketersediaan Data

Dataset yang mendukung kesimpulan penelitian ini tersedia atas permintaan yang wajar kepada corresponding author, tunduk pada persyaratan privasi dan persetujuan etis. Data mentah telah dianonimisasi dan diagregasi untuk melindungi privasi partisipan sambil mempertahankan integritas analitis sesuai dengan standar FAIR (Findable, Accessible, Interoperable, Reusable) data principles. Syntax analisis dan visualisasi tersedia dalam format R/Python untuk mendukung reproducibility.

## Referensi Metodologi

Choi, A., Ooi, A., & Lottridge, D. (2024). Digital Phenotyping for Stress, Anxiety, and Mild Depression: Systematic Literature Review. _JMIR mHealth and uHealth_, 12, e48687. https://www.scopus.com/record/display.uri?eid=2-s2.0-85194022260&origin=scopusAI

Lee, K., Lee, T.C., Yefimova, M., Gilleland Marchak, J., & Nicholas Dionne-Odom, J. (2023). Using digital phenotyping to understand health-related outcomes: A scoping review. _International Journal of Medical Informatics_, 178, 105178. https://www.scopus.com/record/display.uri?eid=2-s2.0-85217210904&origin=scopusAI

Martinez-Martin, N., Greely, H.T., & Cho, M.K. (2021). Ethical development of digital phenotyping tools for mental health applications: Delphi study. _JMIR mHealth and uHealth_, 9(6), e26177. https://www.scopus.com/record/display.uri?eid=2-s2.0-85108108394&origin=scopusAI
